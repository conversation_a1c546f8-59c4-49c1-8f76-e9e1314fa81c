<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoExhibitorDao">

    <resultMap id="ExpoExhibitorMap" type="com.echronos.expo.model.ExpoExhibitor">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerCompanyId" column="cutomer_company_id"/>
        <result property="customerContactsId" column="customer_contacts_id"/>
        <result property="businessMemberId" column="business_member_id"/>
        <result property="boothTotalAmount" column="booth_total_amount"/>
        <result property="leaseTotalAmount" column="lease_total_amount"/>
        <result property="contractStatus" column="contract_status"/>
        <result property="sendEmailStatus" column="send_email_status"/>
        <result property="enterpriseInfoStatus" column="enterprise_info_status"/>
        <result property="journalInfoStatus" column="journal_info_status"/>
        <result property="leaseDemandType" column="lease_demand_type"/>
        <result property="remark" column="remark"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="isAppoint" column="is_appoint"/>
    </resultMap>

    <select id="pageList" resultType="com.echronos.expo.dto.ExpoExhibitorDTO">
        select
            t1.*,
            group_concat(t3.booth_zone) as boothZones,
            group_concat(t3.booth_number) as boothNumbers,
            group_concat(t3.booth_type) as boothTypes,
            group_concat(t3.dimensions) as dimensions,
            t4.customer_name as customerName,
            t5.contact_name as customerContactName,
            t5.phone as customerContactPhone,
            t5.email as customerContractEmail,
            t6.name as businessMemberName
        from
            ech_expo_exhibitor t1
            left join ech_expo_exhibitor_booth t2 on t2.exhibitor_id = t1.id and t2.is_deleted = 0
            left join ech_expo_booth t3 on t3.id = t2.booth_id and t3.is_deleted = 0
            left join ech_crm_new.ech_customer t4 on t4.id = t1.customer_id and t4.is_deleted = 0
            left join ech_crm_new.ech_contact t5 on t5.id = t1.customer_contacts_id and t5.is_deleted = 0
            left join echdb.ech_uc_member t6 on t6.id = t1.business_member_id and t6.is_deleted = 0
        where
            t1.is_deleted = 0
            and t1.expo_id = = #{expoId}
            <if test="null != dto.whereSqlStr and '' != dto.whereSqlStr">
                and ${dto.whereSqlStr}
            </if>
        group
            by t1.id
        <choose>
            <when test="null != dto.sortStr and '' != dto.sortStr">
                order by ${dto.sortStr}
            </when>
            <otherwise>
                order by eb.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="webExhibitorPage" resultType="com.echronos.expo.model.ext.ExpoExhibitorExt">
        select eee.id, ecc.customer_name, ecc.customer_company_id, eee.is_appoint, eee.customer_id, ecc.blurb
        from ech_expo_exhibitor eee
        inner join ech_crm_new.ech_crm_customer ecc on ecc.id = eee.customer_id
        where eee.is_deleted = false
        and ecc.is_deleted = false
        <if test="null != dto.expoId">
            and eee.expo_id = #{dto.expoId}
        </if>
        <if test="null != dto.keyword and '' != dto.keyword">
            and ecc.customer_name like concat ('%',#{dto.keyword},'%')
        </if>
        <if test="null != dto.idList and dto.idList.size() > 0">
            and eee.id in
            <foreach collection="dto.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="null != dto.orderByType and dto.orderByType == 2">
                ORDER BY eee.create_time asc
            </when>
            <when test="null != dto.orderByType and dto.orderByType == 3">
                ORDER BY rand()
            </when>
            <otherwise>
                ORDER BY eee.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="queryAllExhibitorList" resultType="com.echronos.expo.model.ext.ExpoExhibitorExt">
        select eee.id, ecc.customer_name, ecc.customer_company_id, eee.is_appoint, eee.customer_id, ecc.blurb
        from ech_expo_info eei inner join ech_expo_exhibitor eee on eei.id = eee.expo_id
        inner join ech_crm_new.ech_crm_customer ecc on ecc.id = eee.customer_id
        where eee.is_deleted = false
        and ecc.is_deleted = false
        and eei.is_deleted = false
        and eei.company_id = #{dto.companyId}
        <if test="null != dto.keyword and '' != dto.keyword">
            and ecc.customer_name like concat ('%',#{dto.keyword},'%')
        </if>
        group by eee.customer_id
        ORDER BY eee.create_time desc
    </select>

</mapper>

