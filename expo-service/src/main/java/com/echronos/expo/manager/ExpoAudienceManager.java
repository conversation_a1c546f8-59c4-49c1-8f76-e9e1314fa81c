/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoAudienceDao;
import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.dto.ExpoAudiencePageDTO;
import com.echronos.expo.model.ExpoAudience;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * EchExpoAudience Manager
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Component
public class ExpoAudienceManager extends ServiceImpl<ExpoAudienceDao, ExpoAudience> {

    @Resource
    private ExpoAudienceDao expoAudienceDao;

    /**
     * 分页查询观众
     *
     * @param page
     * @param dto
     * @return
     */
    public Page<ExpoAudienceDTO> pageForAudience(Page<ExpoAudienceDTO> page, ExpoAudiencePageDTO dto) {
        return expoAudienceDao.pageForAudience(page, dto);
    }

    /**
     * 查询观众
     * @param dto
     * @return
     */
    public List<ExpoAudienceDTO> listAudience(ExpoAudiencePageDTO dto) {
        return expoAudienceDao.pageForAudience(dto);
    }

    /**
     * 根据观众ID查询观众信息
     * @param id
     * @param expoId
     * @return
     */
    public ExpoAudience queryAudienceById(Integer id, Integer expoId) {
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getId, id)
                .eq(Objects.nonNull(expoId), ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 根据展会ID和邮箱查询观众信息
     * @param expoId
     * @param email
     * @return
     */
    public ExpoAudience queryExistAudience(Integer expoId, String email){
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getExpoId, expoId)
                .eq(ExpoAudience::getEmail, email)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return getOne(lambdaQueryWrapper);
    }


    /**
     * 根据公司ID查询观众总数量
     * @param companyId
     * @param startTime
     * @param endTime
     * @return
     */
    public Integer queryAllAudienceCount(Integer companyId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ExpoAudience> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoAudience::getCompanyId, companyId)
                .eq(ExpoAudience::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .between(ExpoAudience::getCreateTime, startTime, endTime);
        return count(lambdaQueryWrapper);
    }

    /**
     * 查询观众到场次数
     * @param dto
     * @return
     */
    public List<Integer> queryAudienceIdList(ExpoAudienceDTO dto){
        return expoAudienceDao.queryAudienceIdList(dto);
    }
}
