package com.echronos.expo.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.echronos.commons.Result;
import com.echronos.commons.enums.CommonResultCode;
import com.echronos.commons.exception.ParamsValidateException;
import com.echronos.crm.feign.ICustomerClient;
import com.echronos.crm.req.BatchCustomerIdReq;
import com.echronos.crm.req.InsertCustomerReq;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.iform.api.feign.IFormFeign;
import com.echronos.iform.api.req.CopyFormReq;
import com.echronos.mcs.api.feign.IShopSkuFeign;
import com.echronos.mcs.api.req.BaseIdsReq;
import com.echronos.mcs.api.resp.ShopSkuBasisResp;
import com.echronos.order.feign.IOrderFeign;
import com.echronos.order.feign.IPayWayFeign;
import com.echronos.order.req.DefaultPayWayReq;
import com.echronos.order.req.GenOrderParentReq;
import com.echronos.order.req.OrderPaymentInfoReq;
import com.echronos.order.resp.GenOrderResp;
import com.echronos.order.resp.OrderPaymentInfoResp;
import com.echronos.order.resp.PayWayResp;
import com.echronos.pms.enums.TypeEnum;
import com.echronos.pms.feign.IProductFeign;
import com.echronos.pms.feign.ISkuFeign;
import com.echronos.pms.req.AddSkuReq;
import com.echronos.pms.req.BatchAddSkuReq;
import com.echronos.pms.req.QuerySkuReq;
import com.echronos.pms.resp.AddSkuResp;
import com.echronos.pms.resp.SkuListResp;
import com.echronos.search.api.feign.ProductSearchClient;
import com.echronos.search.api.req.PmsProductReq;
import com.echronos.search.api.resp.PmsProductResp;
import com.echronos.system.feign.IMemberFeign;
import com.echronos.system.req.QueryMemberByIdReq;
import com.echronos.system.resp.MemberResp;
import com.echronos.system.resp.member.MemberPowerResp;
import com.echronos.tenant.api.feign.TenantInfoFeignClient;
import com.echronos.tenant.api.req.TenantInfoReq;
import com.echronos.tenant.api.resp.TenantInfoResp;
import com.echronos.user.api.feign.CompanyResourceFeign;
import com.echronos.user.api.feign.IUserService;
import com.echronos.user.api.feign.MemberResourceFeign;
import com.echronos.user.api.req.QueryCompanyIdReq;
import com.echronos.user.api.req.QueryUserReq;
import com.echronos.user.api.resp.UserInfoResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.echronos.user.api.resp.member.BatchMemberInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-01 19:06
 */
@Slf4j
@Service
public class FeignCommonManager {

    @Resource
    private ICustomerClient customerClient;
    @Resource
    private ISkuFeign skuFeign;
    @Resource
    private IProductFeign productFeign;
    @Resource
    private IMemberFeign memberFeign;
    @Resource
    private IUserService iUserService;
    @Resource
    private CompanyResourceFeign companyResourceFeign;
    @Resource
    private IOrderFeign orderFeign;
    @Resource
    private IPayWayFeign payWayFeign;
    @Resource
    private IUserService userService;
    @Resource
    private MemberResourceFeign memberResourceFeign;
    @Resource
    private ProductSearchClient productSearchClient;
    @Resource
    private IShopSkuFeign shopSkuFeign;
    @Resource
    private IFormFeign formFeign;
    @Resource
    private TenantInfoFeignClient tenantInfoFeignClient;

    /**
     * 批量查询客户信息
     *
     * @param customerIdList 客户ID列表
     * @return
     */
    public Map<Integer, CustomerResp> getBatchCustomerByIds(List<Integer> customerIdList) {
        BatchCustomerIdReq req = new BatchCustomerIdReq();
        req.setCustomerIdList(customerIdList);
        Result<List<CustomerResp>> result = customerClient.queryCustomerListByCustomerIdList(req);
        if (!result.isSuccess()) {
            log.error("调用【ICustomerClient.queryCustomerListByCustomerIdList】查询客户异常,req={},error={}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData().stream().collect(Collectors.toMap(CustomerResp::getId, Function.identity(), (key1, key2) -> key2));
    }


    /**
     * 生成商品
     *
     * @param userId     用户ID
     * @param companyId  公司ID
     * @param addSkuList 需生成的商品信息
     * @return 商品及对应的数据ID
     */
    public Map<Integer, AddSkuResp> batchInitSku(Integer userId, Integer companyId, List<AddSkuReq> addSkuList) {
        BatchAddSkuReq req = new BatchAddSkuReq();
        req.setUserId(userId);
        req.setCompanyId(companyId);
        req.setSource(TypeEnum.SourceTypeEnum.INQUIRY_QUOTATION_PRODUCT.getCode());
        req.setAddSkuList(addSkuList);
        Result<List<AddSkuResp>> result = skuFeign.batchAddSku(req);
        if (!result.isSuccess()) {
            log.error("batchInitSku->skuFeign.batchAddSku() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_INIT_SKU_ERROR.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_INIT_SKU_ERROR.getMessage());
        }
        Map<Integer, AddSkuResp> map = result.getData().stream().collect(Collectors.toMap(AddSkuResp::getBusinessId, Function.identity(), (key1, key2) -> key2));
        return map;
    }


    /**
     * 批量根据skuId查询商品基本信息
     *
     * @param skuIdList skuId集合
     * @return 商品基本信息Map结构
     */
    public Map<Integer, SkuListResp> querySkuByIdsToMap(List<Integer> skuIdList) {
        QuerySkuReq querySkuReq = new QuerySkuReq();
        querySkuReq.setList(skuIdList);
        Map<Integer, SkuListResp> skuMap = productFeign.findSkuByIds(querySkuReq);
        return skuMap;
    }

    /**
     * 根据会员ID查询会员信息
     * @param memberId 会员ID
     * @return 会员信息
     */
    public MemberResp getByMemberId(Integer memberId){
        QueryMemberByIdReq req = new QueryMemberByIdReq();
        req.setId(memberId);
        Result<MemberResp> result = memberFeign.getMemberById(req);
        if(!result.isSuccess()){
            log.error("getByMemberId->memberFeign.getMemberById() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
        }
        return result.getData();
    }


    /**
     * 生成客户信息
     *
     * @param req
     */
    public void insertCustomer(InsertCustomerReq req) {
        Result<Boolean> result = customerClient.insertCustomer(req);
        if (!result.isSuccess()) {
            log.error("调用【ICustomerClient.insertCustomer】生成客户信息异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
        }
    }

    /**
     * 根据e-mail或者phone 查询成员信息
     *
     * @param phone
     * @param name
     * @param email
     * @return
     */
    public MemberPowerResp getProprietorshipId(String phone, String name, String email) {
        QueryCompanyIdReq req = new QueryCompanyIdReq();
        req.setPhone(phone);
        req.setCustomerName(name);
        req.setEmail(email);
        req.setDataOrigin(0);
        log.info("查询ech-user个人公司信息req:{}", JSONObject.toJSONString(req));
        Result<MemberPowerResp> result = iUserService.getProprietorshipId(req);
        if (!result.isSuccess()) {
            log.error("根据e-mail查询成员信息【user-api】异常, req = {}, error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量根据公司ID查询公司信息
     * @param companyIdList
     * @return
     */
    public Map<Integer, QueryCompanyResp> queryCompanyByIds(List<Integer> companyIdList){
        Result<Map<Integer, QueryCompanyResp>> result = companyResourceFeign.batchQueryCompanyByIds(companyIdList);
        if (!result.isSuccess()) {
            log.error("批量根据公司ID查询信息【user-api】异常, req = {}, error = {}", JSON.toJSONString(companyIdList), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取服务订单付款方式
     * @param companyId 公司ID
     * @return 付款方式
     */
    public PayWayResp getExpenseOrderPayWay(Integer companyId){
        DefaultPayWayReq req = new DefaultPayWayReq();
        req.setCompanyId(companyId);
        Result<PayWayResp> result = payWayFeign.getExpenseOrderPayWay(req);
        if(!result.isSuccess()){
            log.error("调用【IPayWayFeign.getExpenseOrderPayWay】查询服务订单付款方式异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量生成订单
     * @param req
     * @return
     */
    public Map<Integer, GenOrderResp> addGenOrder(GenOrderParentReq req) {
        Result<Map<Integer, GenOrderResp>> result = orderFeign.genOrder(req);
        if (!result.isSuccess()) {
            log.error("调用【IOrderFeign.genOrder】调用泛用性生成订单接口异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
            throw new ParamsValidateException(CommonResultCode.CommonResultEnum.BAD_REQUEST.getCode(), result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量查询订单付款信息
     * @param orderNoList
     * @return
     */
    public Map<Long, OrderPaymentInfoResp> batchQueryOrderPaymentInfo(List<Long> orderNoList){
        OrderPaymentInfoReq req = new OrderPaymentInfoReq();
        req.setOrderNoList(orderNoList);
        Result<List<OrderPaymentInfoResp>> result = orderFeign.batchQueryOrderPaymentInfo(req);
        if(!result.isSuccess()){
            log.error("调用【IOrderFeign.batchQueryOrderPaymentInfo】批量查询订单付款信息异常, req = {} , error = {}", JSON.toJSONString(req), result.getMessage());
            return null;
        }
        return CollectionUtils.isEmpty(result.getData()) ? new HashMap<>() : result.getData().stream().collect(Collectors.toMap(OrderPaymentInfoResp::getOrderNo, Function.identity()));
    }


    /**
     * 根据用户ID查询用户信息
     * @param req
     * @return
     */
    public UserInfoResp queryUserInfo(QueryUserReq req){
        Result<UserInfoResp> result = userService.getUserById(req);
        if(!result.isSuccess()){
            log.error("根据用户ID查询用户信息【user-api】异常, req ={}, error = {}", JSON.toJSONString(req), result.getMessage());
        }
        return result.getData();
    }


    /**
     * 根据成员ID查询成员(批量)
     *
     * @param memberIds
     * @return
     */
    public Map<Integer, BatchMemberInfoResp> queryMemberDetailsByIds(List<Integer> memberIds) {
        Result<List<BatchMemberInfoResp>> result = memberResourceFeign.queryMemberDetailsByIds(memberIds);
        if (!result.isSuccess()) {
            log.error("<Feign> 调用user服务<根据ID查询成员信息>异常。参数：req={}  error={}", memberIds, result.getMessage());
        }
        Map<Integer, BatchMemberInfoResp> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(result.getData())) {
            map = result.getData().stream().collect(Collectors.toMap(BatchMemberInfoResp::getId, Function.identity(), (key1, key2) -> key2));
        }
        return map;
    }


    /**
     * 调取搜索服务查询商品信息
     * @param pmsProductReq
     * @return
     */
    public Result<List<PmsProductResp>> searchProduct(PmsProductReq pmsProductReq){
        try{
            Result<List<PmsProductResp>> pmsProduct = productSearchClient.findPmsProduct(pmsProductReq);
            if(pmsProduct.getCode() == 0){
                return pmsProduct;
            }
        }catch (Exception e){
            log.error("调取搜索服务查询商品异常,异常信息:{}",e);
        }
        return Result.build();
    }

    /**
     * 批量根据公司ID查询公司信息
     *
     * @param companyId
     * @return
     */
    public QueryCompanyResp selectCompanyById(Integer companyId){
        Result<QueryCompanyResp> result = companyResourceFeign.selectCompanyById(companyId);
        if (!result.isSuccess()) {
            log.error("批量根据公司ID查询信息【user-api】异常, req = {}, error = {}", companyId, result.getMessage());
        }
        return result.getData();
    }

    /**
     * 批量根据店铺商品ID查询商品基础信息
     * @param shopSkuIds 店铺商品ID
     * @return 商品信息
     */
    public Map<Integer, ShopSkuBasisResp> queryShopSkuBasisByIds(Set<Integer> shopSkuIds){
        BaseIdsReq req = new BaseIdsReq();
        req.setIds(shopSkuIds);
        Result<Map<Integer, ShopSkuBasisResp>> result = shopSkuFeign.queryShopSkuBasisByIds(req);
        if(!result.isSuccess()){
            log.error("queryShopSkuBasisByIds->shopSkuFeign.queryShopSkuBasisByIds() 调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode(), CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getMessage());
        }
        return result.getData();
    }

    /**
     * 复制表单
     * @return
     */
    public Map<String, String> copyForm(Integer companyId, List<String> formCodes, Integer userId){
        CopyFormReq req = new CopyFormReq();
        req.setCompanyId(companyId);
        req.setFormCodes(formCodes);
        req.setCreateUser(userId);
        Result<Map<String, String>> result = formFeign.copyForm(req);
        if(!result.isSuccess()){
            log.error("copyForm->formFeign.copyForm() 复制创建表单调用异常：{}, 参数：{}",
                    JSON.toJSONString(result), JSON.toJSONString(req));
            throw new ParamsValidateException(CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getCode(), CommonResultCode.CommonResultEnum.SYSTEM_EXCEPTION.getMessage());
        }
        return result.getData();
    }

    /**
     * 根据租户ID获取租户信息
     *
     * @param tenantId
     * @return
     */
    public TenantInfoResp getTenantInfo(String tenantId) {
        try {
            TenantInfoReq tenantInfoReq = new TenantInfoReq();
            tenantInfoReq.setTenantId(tenantId);
            Result<TenantInfoResp> result = tenantInfoFeignClient.queryTenantInfo(tenantInfoReq);
            if (!result.isSuccess()) {
                log.error("<Feign> 调用tenant服务<根据租户ID或域名查询租户信息>异常，error：{}", result.getMessage());
            } else if (Objects.isNull(result.getData())) {
                log.info("未能根据租户ID或域名查询租户信息，req={}", tenantId);
            }
            return result.getData();
        } catch (Exception e) {
            log.error("tenantInfoFeignClient->queryTenantInfo(): " + e.getMessage());
        }
        return null;
    }
}
