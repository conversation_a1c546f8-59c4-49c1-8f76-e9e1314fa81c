/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dto.ExpoInfoPageDTO;
import com.echronos.expo.model.BaseEntity;
import com.echronos.expo.model.ext.ExpoInfoExt;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.dao.ExpoInfoDao;

import javax.annotation.Resource;
import java.util.List;

import java.util.List;


/**
 * EchExpoInfo Manager
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Component
public class ExpoInfoManager extends ServiceImpl<ExpoInfoDao, ExpoInfo> {

    @Resource
    private ExpoInfoDao expoInfoDao;

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ExpoInfo getById(Integer id){
        LambdaQueryWrapper<ExpoInfo> queryWrapper = new LambdaQueryWrapper<ExpoInfo>()
                .eq(BaseEntity::getId, id)
                .eq(BaseEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据公司id查询展会列表
     * @param companyId 公司ID
     * @return
     */
    public List<ExpoInfo> getListByCompanyId(Integer companyId){
        LambdaQueryWrapper<ExpoInfo> queryWrapper = new LambdaQueryWrapper<ExpoInfo>()
                .eq(ExpoInfo::getCompanyId, companyId)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return list(queryWrapper);
    }

    /**
     * 分页查询展会信息
     * @param companyId
     * @return
     */
    public List<ExpoInfo> queryWebExpoInfo(Integer companyId){
        LambdaQueryWrapper<ExpoInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoInfo::getCompanyId, companyId)
                .eq(ExpoInfo::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .orderByDesc(ExpoInfo::getCreateTime);
        return list(lambdaQueryWrapper);
    }

    /**
     * 分页查询展会信息
     * @param page
     * @param dto
     * @return
     */
    public List<ExpoInfoExt> pageList(Page page, ExpoInfoPageDTO dto){
        return expoInfoDao.pageList(page, dto);
    }

}
