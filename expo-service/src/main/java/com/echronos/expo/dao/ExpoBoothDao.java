/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoBoothDTO;
import com.echronos.expo.dto.ExpoBoothPageDTO;
import com.echronos.expo.model.ExpoBooth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ExpoBooth Dao
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface ExpoBoothDao extends BaseMapper<ExpoBooth> {

    /**
     * 获取展位统计信息
     * @param expoId 展位id
     * @return 结果
     */
    ExpoBoothDTO boothStatistics(@Param("expoId") Integer expoId);

    /**
     * 分页查询
     * @param page 分页参数
     * @param dto 查询参数
     * @return
     */
    List<ExpoBoothDTO> pageList(Page<ExpoBoothDTO> page, @Param("dto") ExpoBoothPageDTO dto);

}
