/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.expo.dto.ExpoAudienceDTO;
import com.echronos.expo.dto.ExpoAudiencePageDTO;
import com.echronos.expo.model.ExpoAudience;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * EchExpoAudience Dao
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface ExpoAudienceDao extends BaseMapper<ExpoAudience> {

    /**
     * 分页查询观众
     *
     * @param page
     * @param dto
     * @return
     */
    Page<ExpoAudienceDTO> pageForAudience(Page<ExpoAudienceDTO> page, @Param("dto") ExpoAudiencePageDTO dto);

    /**
     * 查询观众
     *
     * @param dto
     * @return
     */
    List<ExpoAudienceDTO> pageForAudience(@Param("dto") ExpoAudiencePageDTO dto);

    /**
     * 查询观众到场次数
     * @param dto
     * @return
     */
    List<Integer> queryAudienceIdList(ExpoAudienceDTO dto);
}
