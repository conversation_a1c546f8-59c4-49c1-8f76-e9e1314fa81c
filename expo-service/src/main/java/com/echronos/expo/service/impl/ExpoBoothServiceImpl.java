package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.commons.exception.BusinessException;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.crm.resp.CustomerResp;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.*;
import com.echronos.expo.easyexcel.model.ImportBoothExcelModel;
import com.echronos.expo.enums.ExpoAttachmentFileEnum;
import com.echronos.expo.enums.ExpoBoothEnums;
import com.echronos.expo.enums.ExpoResultCode;
import com.echronos.expo.manager.*;
import com.echronos.expo.model.*;
import com.echronos.expo.service.IExpoBoothService;
import com.echronos.expo.util.AssertUtil;
import com.echronos.expo.util.DateUtil;
import com.echronos.expo.vo.*;
import com.echronos.order.enums.OrderAppTypeEnum;
import com.echronos.order.req.GenOrderParentReq;
import com.echronos.order.req.GenOrderProductReq;
import com.echronos.order.req.GenOrderReq;
import com.echronos.order.resp.GenOrderResp;
import com.echronos.order.resp.PayWayResp;
import com.echronos.pms.enums.TypeEnum;
import com.echronos.pms.req.AddSkuReq;
import com.echronos.pms.req.StandardReq;
import com.echronos.pms.resp.AddSkuResp;
import com.echronos.pms.resp.SkuListResp;
import com.echronos.user.api.resp.company.QueryCompanyResp;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 展位服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class ExpoBoothServiceImpl implements IExpoBoothService {

    @Resource
    private ExpoInfoManager expoInfoManager;
    @Resource
    private ExpoBoothManager expoBoothManager;
    @Resource
    private ExpoAttachmentFileManager expoAttachmentFileManager;
    @Resource
    private FeignCommonManager feignCommonManager;
    @Resource
    private MessageSource messageSource;
    @Resource
    private ExpoBoothOrderManager expoBoothOrderManager;
    @Resource
    private ExpoBoothOrderDetailManager expoBoothOrderDetailManager;
    @Resource
    private ExpoExhibitorManager expoExhibitorManager;
    @Resource
    private ExpoFormManager expoFormManager;
    @Resource
    private ExpoExhibitorBoothManager expoExhibitorBoothManager;


    @Override
    public ExpoBoothStatisticsVO boothStatistics(ExpoBoothDTO dto) {
        ExpoBoothDTO statisticsDTO = expoBoothManager.boothStatistics(dto.getExpoId());
        ExpoBoothStatisticsVO vo = CopyObjectUtils.copyAtoB(statisticsDTO, ExpoBoothStatisticsVO.class);
        return vo;
    }

    @Override
    public List<ExpoAttachmentFileVO> getBoothAttachmentFiles(ExpoBoothDTO dto) {
        return expoAttachmentFileManager.getExpoAttachmentFileList(dto.getExpoId(), dto.getId(), ExpoAttachmentFileEnum.Type.BOOTH_LAYOUT);
    }

    @Override
    public IPage<ExpoBoothVO> pageList(ExpoBoothPageDTO dto) {
        Page<ExpoBoothDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        //查询行转列字段
        FormSqlFieldDTO formSqlFieldDTO = expoFormManager.getFromSqlFieldAndSort("", null, dto.getFilters(), dto.getSort());
        //行转列字段
        dto.setFieldList(formSqlFieldDTO.getRowToColumnSql());
        //筛选条件
        dto.setWhereSqlStr(formSqlFieldDTO.getConditionScreen());
        //排序条件
        dto.setSortStr(formSqlFieldDTO.getBuildSortNew());
        List<ExpoBoothDTO> recordList = expoBoothManager.pageList(page, dto);
        List<ExpoBoothVO> voList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(recordList)){
            Map<Integer, CustomerResp> customerInfoMap = null;
            List<Integer> customerIdList = recordList.stream().filter(e -> null != e.getExhibitorCustomerId())
                    .map(e -> e.getExhibitorCustomerId()).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(customerIdList)){
                customerInfoMap = feignCommonManager.getBatchCustomerByIds(customerIdList);
            }
            for(ExpoBoothDTO record :recordList){
                ExpoBoothVO vo = CopyObjectUtils.copyAtoB(record, ExpoBoothVO.class);
                if(null != record.getExhibitorCustomerId() && null != customerInfoMap
                        && customerInfoMap.containsKey(record.getExhibitorCustomerId())){
                    CustomerResp customerResp = customerInfoMap.get(record.getExhibitorCustomerId());
                    vo.setExhibitorName(customerResp.getName());
                }

                vo.setBoothTypeName(ExpoBoothEnums.BoothType.getMsgByCode(vo.getBoothType()).getMsg());
                vo.setStatusName(ExpoBoothEnums.Status.getByCode(vo.getBoothType()).getMsg());
                voList.add(vo);
            }
        }
        IPage iPage = new Page();
        iPage.setTotal(page.getTotal());
        iPage.setRecords(voList);
        return iPage;
    }


    @Override
    public ExpoBoothDetailVO detail(ExpoBoothPageDTO dto) {
        ExpoBooth expoBooth = checkExist(dto.getId());
        ExpoBoothDetailVO vo = CopyObjectUtils.copyAtoB(expoBooth, ExpoBoothDetailVO.class);
        vo.setBoothTypeName(ExpoBoothEnums.BoothType.getMsgByCode(vo.getBoothType()).getMsg());
        vo.setStatusName(ExpoBoothEnums.Status.getByCode(vo.getBoothType()).getMsg());
        return vo;
    }

    @Override
    public void saveOrUpdateBooth(ExpoBoothDTO dto) {
        if(dto.getPrice().compareTo(BigDecimal.ZERO) <= 0){
            throw new BusinessException(-1, "展位价格不能小于0");
        }
        Integer userId = RequestUserUtils.getUser().getId();
        // 检查展位是否重复
        boolean isDuplicate = expoBoothManager.isBoothDuplicate(
                dto.getExpoId(),
                dto.getBoothName(),
                dto.getBoothFloor(),
                dto.getBoothZone(),
                dto.getBoothNumber(),
                dto.getId() // 编辑时排除当前记录
        );
        if (isDuplicate) {
            throw new BusinessException(-1, "展位已存在，请检查展馆号/名称、楼层、区号/区域、展位号是否重复");
        }
        ExpoBooth booth = CopyObjectUtils.copyAtoB(dto, ExpoBooth.class);
        if (null != dto.getId()) {
            checkExist(dto.getId());
        } else{
            booth.setCreateUser(userId);
            // 默认状态：空闲
            booth.setStatus(ExpoBoothEnums.Status.NOT_SOLD.getCode());
        }
        expoBoothManager.saveOrUpdate(booth);
    }

    @Override
    public void delBooth(ExpoBoothDTO dto) {
        ExpoBooth oldExpoBooth = checkExist(dto.getId());
        if(ExpoBoothEnums.Status.SOLD.getCode() == oldExpoBooth.getStatus()){
            throw new BusinessException(-1, "展位已售出，请勿删除");
        }
        expoBoothManager.removeById(dto.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrder(ExpoBoothOrderDTO dto) {
        // 1. 参数校验
        validateBoothOrderParam(dto);
        // 2. 初始化展位商品
        initBoothSku(dto);

        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());

        // 3. 计算订单总金额
        BigDecimal totalAmount = calculateTotalAmount(dto.getOrderDetailList());

        // 4. 查询客户是否存在
        Map<Integer, CustomerResp> customerMap = feignCommonManager.getBatchCustomerByIds(Lists.newArrayList(dto.getCustomerId()));
        if(null == customerMap || !customerMap.containsKey(dto.getCustomerId())){
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_CUSTOMER_NOT_FOUND.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_CUSTOMER_NOT_FOUND.getMessage());
        }
        CustomerResp customerResp = customerMap.get(dto.getCustomerId());
        dto.setCustomerCompanyId(customerResp.getCustomerCompanyId());
        dto.setTotalAmount(totalAmount);
        dto.setDiscountAmount(dto.getDiscountAmount() != null ? dto.getDiscountAmount() : BigDecimal.ZERO);

        // 5. 添加成为展会展商
        ExpoExhibitor expoExhibitor = expoExhibitorManager.getExhibitorOne(dto.getExpoId(), dto.getCustomerId());
        if(null == expoExhibitor){
            expoExhibitor = new ExpoExhibitor();
            expoExhibitor.setCustomerId(dto.getCustomerId());
            expoExhibitor.setCustomerCompanyId(dto.getCustomerCompanyId());
            expoExhibitor.setCustomerContactsId(dto.getCustomerContactsId());
            expoExhibitor.setBusinessMemberId(dto.getBusinessMemberId());
            expoExhibitor.setBoothTotalAmount(dto.getTotalAmount());
            expoExhibitorManager.save(expoExhibitor);
        }

        // 3. 创建订单主表
        ExpoBoothOrder order = CopyObjectUtils.copyAtoB(dto, ExpoBoothOrder.class);
        order.setExhibitorId(expoExhibitor.getId());
        // 5. 保存订单
        expoBoothOrderManager.save(order);
        // 6. 添加附件
        expoAttachmentFileManager.addOrUpdateAttachmentFile(order.getExpoId(), order.getId(), dto.getAttachmentList(), ExpoAttachmentFileEnum.Type.BOOTH_ORDER);

        // 7. 查询展位信息及商品信息
        List<Integer> idList = dto.getOrderDetailList().stream().map(ExpoBoothOrderDetailDTO::getBoothId).collect(Collectors.toList());
        List<ExpoBooth> boothList = expoBoothManager.listByIds(idList);
        Map<Integer, ExpoBooth> boothMap = boothList.stream().collect(Collectors.toMap(ExpoBooth::getId, Function.identity(), (key1, key2) -> key2));
        List<Integer> skuIdList = boothList.stream().map(ExpoBooth::getSkuId).collect(Collectors.toList());
        Map<Integer, SkuListResp> skuRespMap = feignCommonManager.querySkuByIdsToMap(skuIdList);

        // 8. 创建订单明细
        List<ExpoExhibitorBooth> exhibitorBoothList = new ArrayList<>();
        List<ExpoBoothOrderDetail> orderDetailList = new ArrayList<>();
        GenOrderProductReq productReq;
        List<GenOrderProductReq> orderProductList = new ArrayList<>();
        for (ExpoBoothOrderDetailDTO detailDTO : dto.getOrderDetailList()) {
            ExpoBooth booth = boothMap.get(detailDTO.getBoothId());

            ExpoBoothOrderDetail detail = new ExpoBoothOrderDetail();
            detail.setBoothOrderId(order.getId());
            detail.setBoothId(detailDTO.getBoothId());
            detail.setQuantity(detailDTO.getQuantity());
            detail.setPrice(detailDTO.getPrice());
            detail.setTaxRate(detailDTO.getTaxRate());

            // 获取展位商品信息并设置快照
            SkuListResp skuListResp = skuRespMap.get(booth.getSkuId());
            detail.setSkuId(booth.getSkuId());
            detail.setShopSkuId(booth.getShopSkuId());
            detail.setSkuCode(skuListResp.getSkuCode());
            detail.setName(skuListResp.getName());
            detail.setStandardJson(skuListResp.getStandardJson());
            detail.setMarketPrice(skuListResp.getMarketPrice());
            detail.setUnit(skuListResp.getUnit());
            orderDetailList.add(detail);

            // 展商与展位关联
            ExpoExhibitorBooth exhibitorBooth = new ExpoExhibitorBooth();
            exhibitorBooth.setBoothId(booth.getId());
            exhibitorBooth.setExhibitorId(expoExhibitor.getId());
            exhibitorBoothList.add(exhibitorBooth);

            // 订单需要的参数
            productReq = new GenOrderProductReq();
            productReq.setSkuId(booth.getSkuId());
            productReq.setShopSkuId(booth.getShopSkuId());
            productReq.setCategoryId(skuListResp.getCategoryId());
            productReq.setCategoryName(skuListResp.getCategoryName());
            productReq.setProductName(skuListResp.getName());
            productReq.setNumber(BigDecimal.ONE);
            productReq.setProductUnit(skuListResp.getUnit());
            productReq.setProductUnitNum(skuListResp.getUnitNum());
            productReq.setPriceNum(skuListResp.getPriceNum());
            productReq.setSkuCode(skuListResp.getSkuCode());
            productReq.setProductPrice(detailDTO.getPrice());
            productReq.setProductMarketPrice(skuListResp.getMarketPrice());
            productReq.setProductMainPic(CollectionUtil.isNotEmpty(skuListResp.getImageList()) ? skuListResp.getImageList().get(0) : null);
            productReq.setProductSpecificationList(CopyObjectUtils.copyAlistToBlist(skuListResp.getStandardList(), com.echronos.order.req.StandardReq.class));
            productReq.setHaveTranFee(CommonStatus.YesOrNoEnum.NO.getValue());
            productReq.setBrandId(skuListResp.getBrandId());
            productReq.setBrandName(skuListResp.getBrandName());
            productReq.setBarsCode(skuListResp.getBarsCode());
            productReq.setSkuCode(skuListResp.getSkuCode());
            productReq.setBelongtoCompanyId(expoInfo.getCompanyId());
            productReq.setIsGift(CommonStatus.YesOrNoEnum.NO.getValue());
            orderProductList.add(productReq);

        }
        // 9. 展商订单与订单明细关联
        expoBoothOrderDetailManager.saveBatch(orderDetailList);

        // 10. 展商与展位关联
        expoExhibitorBoothManager.saveBatch(exhibitorBoothList);

        // 11. 调用订单创建接口
        PayWayResp expenseOrderPayWay = feignCommonManager.getExpenseOrderPayWay(expoInfo.getCompanyId());
        QueryCompanyResp sellerCompanyResp = feignCommonManager.queryCompanyByIds(Lists.newArrayList(expoInfo.getCompanyId())).get(0);
        GenOrderReq orderReq = new GenOrderReq();
        orderReq.setSerialNumber(order.getId());
        orderReq.setOrderAppType(null);
        orderReq.setIsOnline(false);
        orderReq.setCoreDocumentId(order.getId());
        orderReq.setBuyerMemberId(customerResp.getMemberId());
        orderReq.setBuyerName(customerResp.getName());
        orderReq.setBuyerCompanyId(customerResp.getCompanyId());
        orderReq.setBuyerCompanyName(customerResp.getCompanyName());
        orderReq.setSellerCompanyId(expoInfo.getCompanyId());
        orderReq.setSellerCompanyName(sellerCompanyResp.getCompanyName());
        orderReq.setPayWayNo(expenseOrderPayWay.getPayWayNo());
        orderReq.setOrderProductList(orderProductList);
        GenOrderParentReq req = new GenOrderParentReq();
        req.setRequestUserId(dto.getUserId());
        req.setRequestMemberId(dto.getMemberId());
        req.setCurrentCompanyId(dto.getCompanyId());
        req.setGenOrderReqList(Lists.newArrayList(orderReq));
        Map<Integer, GenOrderResp> genOrderRespMap = feignCommonManager.addGenOrder(req);
        GenOrderResp genOrderResp = genOrderRespMap.get(order.getId());

        // 12. 保存订单编号
        ExpoBoothOrder updateBothOrder = new ExpoBoothOrder();
        updateBothOrder.setId(order.getId());
        updateBothOrder.setOrderNo(genOrderResp.getOrderNo());
        expoBoothOrderManager.updateById(updateBothOrder);

    }

    /**
     * 校验展位订单参数
     * @param dto 订单参数
     */
    private void validateBoothOrderParam(ExpoBoothOrderDTO dto) {
        List<Integer> boothIdList = dto.getOrderDetailList().stream().map(BaseNotTenantEntity::getId).collect(Collectors.toList());
        List<ExpoBooth> boothList = expoBoothManager.getBoothByIds(boothIdList);
        Map<Integer, ExpoBooth> boothMap = boothList.stream().collect(Collectors.toMap(ExpoBooth::getId, Function.identity(), (key1, key2) -> key2));
        // 校验展位是否存在且可售
        for (ExpoBoothOrderDetailDTO detail : dto.getOrderDetailList()) {
            ExpoBooth booth = boothMap.get(detail.getBoothId());
            if (booth == null) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_NOT_FOUND.getCode(),
                        ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_NOT_FOUND.getMessage());
            }
            if (!ExpoBoothEnums.Status.SOLD.getCode().equals(booth.getStatus())) {
                throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_ALREADY_SOLD.getCode(),
                        String.format(ExpoResultCode.ExpoResultEnum.EXPO_BOOTH_ALREADY_SOLD.getMessage(), booth.getBoothNumber()));
            }
        }

        // 校验优惠金额不能超过总金额
        BigDecimal totalAmount = calculateTotalAmount(dto.getOrderDetailList());
        if (dto.getDiscountAmount() != null && dto.getDiscountAmount().compareTo(totalAmount) > 0) {
            throw new BusinessException(ExpoResultCode.ExpoResultEnum.EXPO_DISCOUNT_AMOUNT_EXCEED.getCode(),
                    ExpoResultCode.ExpoResultEnum.EXPO_DISCOUNT_AMOUNT_EXCEED.getMessage());
        }
    }

    /**
     * 初始化展位商品
     * @param dto 订单参数
     */
    private void initBoothSku(ExpoBoothOrderDTO dto){
        List<Integer> idList = dto.getOrderDetailList().stream().map(ExpoBoothOrderDetailDTO::getBoothId).collect(Collectors.toList());
        List<ExpoBooth> boothList = expoBoothManager.listByIds(idList);
        List<AddSkuReq> addSkuReqList = new ArrayList<>();
        for (ExpoBooth booth : boothList) {
            if(null != booth.getSkuId()){
                continue;
            }
            AddSkuReq addSkuReq = new AddSkuReq();
            addSkuReq.setBusinessId(booth.getId());
            addSkuReq.setProductName(booth.getBoothName());
            //单位
            addSkuReq.setUnit(ExpoBoothEnums.GoodsStandType.UNIT.getMsg());

            List<StandardReq> standardList = new ArrayList<>();
            StandardReq standardReq1 = new StandardReq();
            //展位类型
            standardReq1.setName(ExpoBoothEnums.GoodsStandType.TYPE.getMsg());
            standardReq1.setValue(ExpoBoothEnums.BoothType.getMsgByCode(booth.getBoothType()).getMsg());
            StandardReq standardReq2 = new StandardReq();
            //展位尺寸
            standardReq2.setName(ExpoBoothEnums.GoodsStandType.SIZE.getMsg());
            standardReq2.setValue(booth.getDimensions());
            standardList.add(standardReq1);
            standardList.add(standardReq2);

            addSkuReq.setStandardList(standardList);
            addSkuReq.setMarketPrice(booth.getPrice());
            addSkuReq.setSaleGroup(BigDecimal.ONE);
            addSkuReq.setDetailReqList(null);
            addSkuReq.setStatus(TypeEnum.PmsStatusTypeEnum.ALREADYTOP.getValue());
            addSkuReq.setType(TypeEnum.PmsSkuTypeEnum.ORDINARY_PRODUCT.getCode());
            addSkuReqList.add(addSkuReq);
        }
        if(CollectionUtil.isNotEmpty(addSkuReqList)){
            // 初始化商品
            Map<Integer, AddSkuResp> addSkuRespMap = feignCommonManager.batchInitSku(dto.getUserId(), dto.getCompanyId(), addSkuReqList);
            List<ExpoBooth> updateBoothList = new ArrayList<>();
            for (ExpoBooth booth : boothList) {
                AddSkuResp addSkuResp = addSkuRespMap.get(booth.getId());
                if(null != addSkuResp){
                    ExpoBooth updateBooth = new ExpoBooth();
                    updateBooth.setId(booth.getId());
                    updateBooth.setSkuId(addSkuResp.getSkuId());
                    updateBooth.setShopSkuId(addSkuResp.getShopSkuId());
                    updateBoothList.add(updateBooth);
                }
            }
            if(CollectionUtil.isNotEmpty(updateBoothList)){
                expoBoothManager.updateBatchById(updateBoothList);
            }
        }
    }

    /**
     * 计算订单总金额
     * @param orderDetails 订单明细
     * @return 总金额
     */
    private BigDecimal calculateTotalAmount(List<ExpoBoothOrderDetailDTO> orderDetails) {
        return orderDetails.stream()
                .map(detail -> detail.getPrice().multiply(detail.getQuantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public ExpoBoothImportVO importTemplate(ExpoBoothDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if(null == expoInfo){
            throw new BusinessException(-1, "展馆不存在");
        }
        // 读取的数据
        List<ImportBoothExcelModel> readDataList = EasyExcel.read(dto.getFileUrl())
                .sheet(0)
                .headRowNumber(1)
                .head(ImportBoothExcelModel.class)
                .doReadSync();
        Integer totalCount = 0;
        Integer successCount = 0;
        Integer errorCount = 0;
        List<ImportErrorVO> allErrorMsgList = new ArrayList<>();
        List<ExpoBooth> addList = new ArrayList<>();
        if(CollectionUtil.isEmpty(readDataList)){
            throw new BusinessException(-1, "导入数据为空");
        }else{
            // 检查展位名称是否重复
            List<ExpoBooth> existBoothList = expoBoothManager.getBoothByExpoId(dto.getExpoId());
            List<String> existBoothNameList = null;
            if(CollectionUtil.isNotEmpty(existBoothList)){
                existBoothNameList = existBoothList.stream().map(e -> e.getBoothName().concat(e.getBoothFloor())
                        .concat(e.getBoothZone()).concat(e.getBoothNumber())).collect(Collectors.toList());
            }
            // 导入的展位名称
            Map<String, Integer> importExistNameMap = new HashMap<>();
            totalCount = readDataList.size();
            for (int i = 0; i < readDataList.size(); i++) {
                ImportBoothExcelModel excelModel = readDataList.get(i);
                List<String> errorMsgList = new ArrayList<>();
                if(AssertUtil.checkNull(excelModel.getBoothName())){
                    errorMsgList.add("展位名称不能为空");
                }else if(excelModel.getBoothName().length() > 30){
                    errorMsgList.add("展位名称长度不能超过30个字符");
                }
                if(AssertUtil.checkNull(excelModel.getBoothFloor())){
                    errorMsgList.add("展位楼层不能为空");
                }else if(excelModel.getBoothFloor().length() > 10){
                    errorMsgList.add("展位楼层长度不能超过10个字符");
                }
                if(AssertUtil.checkNull(excelModel.getBoothZone())){
                    errorMsgList.add("展位区域不能为空");
                }else if(excelModel.getBoothZone().length() > 10){
                    errorMsgList.add("展位区域长度不能超过10个字符");
                }
                if(AssertUtil.checkNull(excelModel.getBoothNumber())){
                    errorMsgList.add("展位号不能为空");
                }else if(excelModel.getBoothNumber().length() > 10){
                    errorMsgList.add("展位号长度不能超过10个字符");
                }
                if(AssertUtil.checkNull(excelModel.getBoothType())){
                    errorMsgList.add("展位类型不能为空");
                } else {
                    ExpoBoothEnums.BoothType boothType = ExpoBoothEnums.BoothType.getByMsg(excelModel.getBoothType());
                    if(null == boothType){
                        errorMsgList.add("展位类型错误");
                    }
                }
                if(AssertUtil.checkNull(excelModel.getDimensions())){
                    errorMsgList.add("展位尺寸不能为空");
                }else if(excelModel.getDimensions().length() > 10){
                    errorMsgList.add("展位尺寸长度不能超过10个字符");
                }
                if(AssertUtil.checkNull(excelModel.getPrice())){
                    errorMsgList.add("展位价格不能为空");
                } else if(excelModel.getPrice().compareTo(BigDecimal.ZERO) <= 0){
                    errorMsgList.add("展位价格必须大于0");
                } else if(excelModel.getPrice().compareTo(new BigDecimal("999999999")) >= 0){
                    errorMsgList.add("展位价格不能大于999999999");
                }
                String uniqueName = excelModel.getBoothName().concat(excelModel.getBoothFloor()).concat(excelModel.getBoothZone()).concat(excelModel.getBoothNumber());
                if(importExistNameMap.containsKey(uniqueName)){
                    errorMsgList.add("展位名称重复");
                }else if(CollectionUtil.isNotEmpty(existBoothNameList) && existBoothNameList.contains(uniqueName)){
                    errorMsgList.add("展位名称与以有展位数据重复");
                }
                if(CollectionUtil.isNotEmpty(errorMsgList)){
                    Map<String, String> fieldValueMap = new HashMap<>();
                    Field[] fields = excelModel.getClass().getDeclaredFields();
                    for(Field field : fields){
                        //设置允许通过反射访问私有变量
                        field.setAccessible(true);
                        ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                        String[] excelTitleName = excelProperty.value();
                        // 字段名
                        String filedName = messageSource.getMessage(excelTitleName[excelTitleName.length - 1], null,
                                excelTitleName[excelTitleName.length - 1], LocaleContextHolder.getLocale());
                        // 字段值
                        String filedValue = null;
                        try {
                            Object filedVal = field.get(excelModel);
                            filedValue  = null != filedVal ? filedVal.toString() : "";
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                        fieldValueMap.put(filedName, filedValue);
                    }
                    ImportErrorVO importErrorVO = new ImportErrorVO();
                    importErrorVO.setRowNum(i + 1);
                    importErrorVO.setFieldValueMap(fieldValueMap);
                    importErrorVO.setErrorMsgList(errorMsgList);
                    allErrorMsgList.add(importErrorVO);
                    errorCount++;
                }else{
                    successCount++;
                    ExpoBooth expoBooth = CopyObjectUtils.copyAtoB(excelModel, ExpoBooth.class);
                    expoBooth.setExpoId(dto.getExpoId());
                    addList.add(expoBooth);
                }
            }
        }
        if(CollectionUtil.isEmpty(allErrorMsgList)){
            expoBoothManager.saveBatch(addList);
        }
        return ExpoBoothImportVO.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .errorCount(errorCount)
                .errorMsgList(allErrorMsgList)
                .build();
    }

    @Override
    public ExpoBoothConversionRateVO expoBoothSale(ExpoBoothDTO dto) {
        //查询展位的总数量
        List<ExpoBooth> expoBoothList = expoBoothManager.getBoothByExpoId(dto.getExpoId());
        if(CollectionUtil.isEmpty(expoBoothList)){
            return null;
        }
        //过滤出已售展位
        List<ExpoBooth> soldExpoBoothList = expoBoothList.stream().filter(e -> ExpoBoothEnums.Status.SOLD.code()
                .equals(e.getStatus())).collect(Collectors.toList());
        //获取到相除之后的结果
        BigDecimal divide = new BigDecimal(soldExpoBoothList.size()).divide(new BigDecimal(expoBoothList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
        BigDecimal conversionRate = divide.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED));
        ExpoBoothConversionRateVO vo = new ExpoBoothConversionRateVO();
        vo.setConversionRate(conversionRate);
        return vo;
    }

    @Override
    public List<ExpoBoothTypeVO> expoBootType(ExpoBoothDTO dto) {
        //查询展位的总数量
        List<ExpoBooth> expoBoothList = expoBoothManager.getBoothByExpoId(dto.getExpoId());
        if(CollectionUtil.isEmpty(expoBoothList)){
            return new ArrayList<>();
        }
        //根据展位类型进行分组
        Map<Integer, List<ExpoBooth>> expoBoothMap = expoBoothList.stream().collect(Collectors.groupingBy(ExpoBooth::getBoothType));
        //返回对象集合
        List<ExpoBoothTypeVO> voList = new ArrayList<>();
        ExpoBoothTypeVO vo = null;
        for (Map.Entry<Integer, List<ExpoBooth>> entry : expoBoothMap.entrySet()) {
            vo = new ExpoBoothTypeVO();
            vo.setBoothType(entry.getKey());
            vo.setBoothTypeName(ExpoBoothEnums.BoothType.getMsgByCode(entry.getKey()).getMsg());
            //获取到未售展位的集合
            List<ExpoBooth> notSeleBoothList = entry.getValue().stream().filter(e ->
                    ExpoBoothEnums.Status.NOT_SOLD.code().equals(e.getStatus())).collect(Collectors.toList());
            //获取到已售展位的集合
            List<ExpoBooth> seleBoothList = entry.getValue().stream().filter(e ->
                    ExpoBoothEnums.Status.SOLD.code().equals(e.getStatus())).collect(Collectors.toList());
            vo.setNoSaleNumber(CollectionUtil.isNotEmpty(notSeleBoothList) ? notSeleBoothList.size() : NumberConstant.ZERO);
            vo.setSaleNumber(CollectionUtil.isNotEmpty(seleBoothList) ? seleBoothList.size() : NumberConstant.ZERO);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public ExpoBoothReserveRateVO expoBoothReserveRate(ExpoBoothDTO dto) {
        List<ExpoBooth> expoBoothList = expoBoothManager.getBoothByExpoId(dto.getExpoId());
        if(CollectionUtil.isEmpty(expoBoothList)){
            return null;
        }
        ExpoBoothReserveRateVO vo = new ExpoBoothReserveRateVO();
        //获取当月的开始时间
        LocalDateTime startTime = DateUtil.getTimesMonthMorning().minusHours(NumberConstant.EIGHT);
        LocalDateTime endTime = LocalDateTime.now();
        //查询已售展位
        List<ExpoBooth> soldBoothList = expoBoothManager.queryBoothByCreateTime(dto.getExpoId(), startTime, endTime);
        //查询上个月的开始时间和结束时间
        LocalDateTime lastMonthStartTime = DateUtil.getFirstDayOfLastMonth();
        LocalDateTime lastMonthEndTime = DateUtil.getLastDayOfLastMonth();
        //上个月已售展位数量
        List<ExpoBooth> lastMonthSoldBoothList = expoBoothManager.
                queryBoothByCreateTime(dto.getExpoId(), lastMonthStartTime, lastMonthEndTime);
        //两者之间的差值
        BigDecimal difference = new BigDecimal(soldBoothList.size()).subtract(new BigDecimal(lastMonthSoldBoothList.size()));
        //转换率类型  10 增 20 减
        Integer increaseType = difference.compareTo(new BigDecimal(NumberConstant.ZERO)) < NumberConstant.ZERO ?
                NumberConstant.TEN : NumberConstant.TWENTY;
        //转换率
        BigDecimal increaseRate = difference.divide(new BigDecimal(expoBoothList.size()))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        //当月转换率
        BigDecimal thisMonthRate = new BigDecimal(soldBoothList.size()).divide(new BigDecimal(expoBoothList.size()))
                .multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
        //当月的增长率
        vo.setIncreaseRate(increaseRate);
        vo.setIncreaseType(increaseType);
        vo.setReserveRate(thisMonthRate);
        return vo;
    }

    /**
     * 检查展位是否存在
     * @param id
     */
    private ExpoBooth checkExist(Integer id){
        ExpoBooth oldExpoBooth = expoBoothManager.getBoothById(id);
        if(null == oldExpoBooth){
            throw new BusinessException(-1, "展位不存在");
        }
        return oldExpoBooth;
    }



}
