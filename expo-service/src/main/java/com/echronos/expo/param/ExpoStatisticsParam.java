package com.echronos.expo.param;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * date2025/8/11 15:04
 */
@Data
public class ExpoStatisticsParam {

    /**
     * 展商ID
     */
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;

    /**
     * 时间筛选类型 10 本年度 20 去年  30 本月
     */
    @NotNull(message = "{NOTNULL.DATE.SCREE.TYPE}")
    private Integer dateScreeType;

}
