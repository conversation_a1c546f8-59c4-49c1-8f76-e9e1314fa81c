package com.echronos.expo.schedule;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.echronos.expo.enums.ExpoBusinessTypeEnum;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import com.echronos.job.annotation.ElasticSimpleJob;
import com.echronos.job.base.SimpleJobAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 展前24H预约未响应通知任务
 *
 * <AUTHOR>
 * @date 2025/8/14 16:45
 */
@Slf4j
@ElasticSimpleJob(corn = "0 0 9 * * ?", disabled = false, overwrite = true)
public class ExpoAppointmentNoticeTask extends SimpleJobAbstract {

    @Resource
    private ExpoAppointmentManager expoAppointmentManager;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("开始执行展前24H预约未响应通知任务");
        // 查询展前24H未响应的预约
        List<ExpoAppointmentExt> list = expoAppointmentManager.queryBeforeExpoNoResponse();
        // todo 发im通知
        if (!CollectionUtils.isEmpty(list)) {
            // 总数
            int total = list.size();
            // 展商预约观众数
            long exhibitorTotal = list.stream().filter(v -> ExpoBusinessTypeEnum.EXHIBITOR.getCode().equals(v.getBusinessType())).count();
            // 观众预约展商数
            long audienceTotal = list.stream().filter(v -> ExpoBusinessTypeEnum.AUDIENCE.getCode().equals(v.getBusinessType())).count();

        }
    }
}
