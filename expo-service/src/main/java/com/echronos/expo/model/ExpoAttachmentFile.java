/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.model;


import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * ExpoAttachmentFile 实体类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@TableName("ech_expo_attachment_file")
public class ExpoAttachmentFile extends BaseEntity{

    /**
     *  展会id 
     */
    private Integer expoId;
    /**
     *  业务id 
     */
    private Integer businessId;
    /**
     *  文件名称 
     */
    private String fileName;
    /**
     *  文件大小 
     */
    private BigDecimal fileSize;
    /**
     *  附件地址 
     */
    private String filePath;
    /**
     *  展会附件类型：1.展会手册  2.展会海报模板  3.展位订单 
     */
    private Integer type;

}